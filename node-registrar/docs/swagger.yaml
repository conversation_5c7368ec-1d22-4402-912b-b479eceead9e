basePath: /api/v1
definitions:
  db.Account:
    properties:
      created_at:
        type: string
      public_key:
        description: |-
          The public key (ED25519 for nodes, ED25519 or SR25519 for farmers) in the more standard base64 since we are moving from substrate echo system?
          (still SS58 can be used or plain base58 ,TBD)
        type: string
      relays:
        description: Optional list of relay domains
        items:
          type: string
        type: array
      rmb_enc_key:
        description: Optional base64 encoded public key for rmb communication
        type: string
      twin_id:
        type: integer
      updated_at:
        type: string
    type: object
  db.Farm:
    properties:
      created_at:
        type: string
      dedicated:
        type: boolean
      farm_id:
        type: integer
      farm_name:
        type: string
      stellar_address:
        type: string
      twin_id:
        description: Farmer account reference
        type: integer
      updated_at:
        type: string
    required:
    - farm_name
    - stellar_address
    - twin_id
    type: object
  db.Interface:
    properties:
      ips:
        items:
          type: string
        type: array
      mac:
        type: string
      name:
        type: string
    type: object
  db.Location:
    properties:
      city:
        type: string
      country:
        type: string
      latitude:
        type: string
      longitude:
        type: string
    type: object
  db.Node:
    properties:
      approved:
        type: boolean
      created_at:
        type: string
      farm_id:
        description: Constraints set to prevents unintended account deletion if linked
          Farms/nodes exist.
        type: integer
      interfaces:
        items:
          $ref: '#/definitions/db.Interface'
        type: array
      last_seen:
        description: Last time the node sent Uptime report
        type: string
      location:
        $ref: '#/definitions/db.Location'
      node_id:
        type: integer
      online:
        description: Computed field, not stored in database
        type: boolean
      resources:
        allOf:
        - $ref: '#/definitions/db.Resources'
        description: PublicConfig PublicConfig `json:"public_config" gorm:"type:json"`
      secure_boot:
        type: boolean
      serial_number:
        type: string
      twin_id:
        description: Node account reference
        type: integer
      updated_at:
        type: string
      virtualized:
        type: boolean
    type: object
  db.Resources:
    properties:
      cru:
        type: integer
      hru:
        type: integer
      mru:
        type: integer
      sru:
        type: integer
    type: object
  server.AccountCreationRequest:
    properties:
      public_key:
        description: base64 encoded
        type: string
      relays:
        items:
          type: string
        type: array
      rmb_enc_key:
        type: string
      signature:
        description: |-
          the registrar expect a signature of a message with format `timestampStr:publicKeyBase64`
          - signature format: base64(ed25519_or_sr22519_signature)
        type: string
      timestamp:
        type: integer
    required:
    - public_key
    - signature
    - timestamp
    type: object
  server.NodeRegistrationRequest:
    properties:
      farm_id:
        minimum: 1
        type: integer
      interfaces:
        items:
          $ref: '#/definitions/db.Interface'
        type: array
      location:
        $ref: '#/definitions/db.Location'
      resources:
        $ref: '#/definitions/db.Resources'
      secure_boot:
        type: boolean
      serial_number:
        type: string
      twin_id:
        minimum: 1
        type: integer
      virtualized:
        type: boolean
    required:
    - farm_id
    - interfaces
    - location
    - resources
    - serial_number
    - twin_id
    type: object
  server.UpdateAccountRequest:
    properties:
      relays:
        items:
          type: string
        type: array
      rmb_enc_key:
        type: string
    type: object
  server.UpdateFarmRequest:
    properties:
      farm_name:
        maxLength: 40
        type: string
      stellar_address:
        type: string
    type: object
  server.UpdateNodeRequest:
    properties:
      farm_id:
        minimum: 1
        type: integer
      interfaces:
        items:
          $ref: '#/definitions/db.Interface'
        type: array
      location:
        $ref: '#/definitions/db.Location'
      resources:
        $ref: '#/definitions/db.Resources'
      secure_boot:
        type: boolean
      serial_number:
        type: string
      virtualized:
        type: boolean
    required:
    - farm_id
    - interfaces
    - location
    - resources
    - serial_number
    type: object
  server.UptimeReportRequest:
    properties:
      timestamp:
        type: integer
      uptime:
        type: integer
    required:
    - timestamp
    - uptime
    type: object
  server.ZOSVersionRequest:
    properties:
      version:
        type: string
    required:
    - version
    type: object
info:
  contact: {}
  description: API for managing TFGrid node registration
  title: Node Registrar API
  version: "1.0"
paths:
  /accounts:
    get:
      consumes:
      - application/json
      description: This endpoint retrieves an account by its twin ID or public key.
      parameters:
      - description: Twin ID of the account
        in: query
        name: twin_id
        type: integer
      - description: Base64 decoded Public key of the account
        in: query
        name: public_key
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: Account details
          schema:
            $ref: '#/definitions/db.Account'
        "400":
          description: Invalid request
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Account not found
          schema:
            additionalProperties: true
            type: object
      summary: Retrieve an account by twin ID or public key
      tags:
      - accounts
    post:
      consumes:
      - application/json
      description: Create a new twin account with cryptographic verification
      parameters:
      - description: Account creation data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/server.AccountCreationRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Created account details
          schema:
            $ref: '#/definitions/db.Account'
        "400":
          description: Invalid request
          schema:
            additionalProperties: true
            type: object
        "409":
          description: Account already exists
          schema:
            additionalProperties: true
            type: object
      summary: Create new account
      tags:
      - accounts
  /accounts/{twin_id}:
    patch:
      consumes:
      - application/json
      description: Updates an account's relays and RMB encryption key
      parameters:
      - description: 'Authentication format: Base64(<unix_timestamp>:<twin_id>):Base64(signature)'
        in: header
        name: X-Auth
        required: true
        type: string
      - description: Twin ID of the account
        in: path
        name: twin_id
        required: true
        type: integer
      - description: Account details to update
        in: body
        name: account
        required: true
        schema:
          $ref: '#/definitions/server.UpdateAccountRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Account updated successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Account not found
          schema:
            additionalProperties: true
            type: object
      summary: Update account details
      tags:
      - accounts
  /farms:
    get:
      consumes:
      - application/json
      description: Get a list of farms with optional filters
      parameters:
      - description: Filter by farm name
        in: query
        name: farm_name
        type: string
      - description: Filter by farm ID
        in: query
        name: farm_id
        type: integer
      - description: Filter by twin ID
        in: query
        name: twin_id
        type: integer
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Results per page
        in: query
        name: size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of farms
          schema:
            items:
              $ref: '#/definitions/db.Farm'
            type: array
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
      summary: List farms
      tags:
      - farms
    post:
      consumes:
      - application/json
      description: Create a new farm entry
      parameters:
      - description: 'Authentication format: Base64(<unix_timestamp>:<twin_id>):Base64(signature)'
        in: header
        name: X-Auth
        required: true
        type: string
      - description: Farm creation data
        in: body
        name: farm
        required: true
        schema:
          $ref: '#/definitions/db.Farm'
      produces:
      - application/json
      responses:
        "201":
          description: '''farm_id'': farmID"]'
          schema:
            additionalProperties:
              type: integer
            type: object
        "400":
          description: Invalid request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "409":
          description: Farm already exists
          schema:
            additionalProperties: true
            type: object
      summary: Create new farm
      tags:
      - farms
  /farms/{farm_id}:
    get:
      consumes:
      - application/json
      description: Get details for a specific farm
      parameters:
      - description: Farm ID
        in: path
        name: farm_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Farm details
          schema:
            $ref: '#/definitions/db.Farm'
        "400":
          description: Invalid farm ID
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Farm not found
          schema:
            additionalProperties: true
            type: object
      summary: Get farm details
      tags:
      - farms
    patch:
      consumes:
      - application/json
      description: Update existing farm details
      parameters:
      - description: 'Authentication format: Base64(<unix_timestamp>:<twin_id>):Base64(signature)'
        in: header
        name: X-Auth
        required: true
        type: string
      - description: Farm ID
        in: path
        name: farm_id
        required: true
        type: integer
      - description: Farm update data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/server.UpdateFarmRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Farm updated successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Farm not found
          schema:
            additionalProperties: true
            type: object
      summary: Update farm
      tags:
      - farms
  /nodes:
    get:
      consumes:
      - application/json
      description: Get a list of nodes with optional filters
      parameters:
      - description: Filter by node ID
        in: query
        name: node_id
        type: integer
      - description: Filter by farm ID
        in: query
        name: farm_id
        type: integer
      - description: Filter by twin ID
        in: query
        name: twin_id
        type: integer
      - description: Filter by status
        in: query
        name: status
        type: string
      - description: Filter by health status
        in: query
        name: healthy
        type: boolean
      - description: Filter by online status (true = online, false = offline)
        in: query
        name: online
        type: boolean
      - description: Filter nodes last seen within this many minutes
        in: query
        name: last_seen
        type: integer
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Results per page
        in: query
        name: size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: List of nodes with online status
          schema:
            items:
              $ref: '#/definitions/db.Node'
            type: array
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
      summary: List nodes
      tags:
      - nodes
    post:
      consumes:
      - application/json
      description: Register a new node in the system
      parameters:
      - description: 'Authentication format: Base64(<unix_timestamp>:<twin_id>):Base64(signature)'
        in: header
        name: X-Auth
        required: true
        type: string
      - description: Node registration data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/server.NodeRegistrationRequest'
      produces:
      - application/json
      responses:
        "201":
          description: '''node_id'': nodeID'
          schema:
            additionalProperties:
              type: integer
            type: object
        "400":
          description: Invalid request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "409":
          description: Node already exists
          schema:
            additionalProperties: true
            type: object
      summary: Register new node
      tags:
      - nodes
  /nodes/{node_id}:
    get:
      consumes:
      - application/json
      description: Get details for a specific node
      parameters:
      - description: Node ID
        in: path
        name: node_id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Node details with online status and last_seen information
          schema:
            $ref: '#/definitions/db.Node'
        "400":
          description: Invalid node ID
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Node not found
          schema:
            additionalProperties: true
            type: object
      summary: Get node details
      tags:
      - nodes
    patch:
      consumes:
      - application/json
      description: Update existing node details
      parameters:
      - description: 'Authentication format: Base64(<unix_timestamp>:<twin_id>):Base64(signature)'
        in: header
        name: X-Auth
        required: true
        type: string
      - description: Node ID
        in: path
        name: node_id
        required: true
        type: integer
      - description: Node update data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/server.UpdateNodeRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Node updated successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Node not found
          schema:
            additionalProperties: true
            type: object
      summary: Update node
      tags:
      - nodes
  /nodes/{node_id}/uptime:
    post:
      consumes:
      - application/json
      description: Submit uptime report for a node
      parameters:
      - description: 'Authentication format: Base64(<unix_timestamp>:<twin_id>):Base64(signature)'
        in: header
        name: X-Auth
        required: true
        type: string
      - description: Node ID
        in: path
        name: node_id
        required: true
        type: integer
      - description: Uptime report data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/server.UptimeReportRequest'
      produces:
      - application/json
      responses:
        "201":
          description: Uptime reported successfully
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Invalid request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "404":
          description: Node not found
          schema:
            additionalProperties: true
            type: object
      summary: Report node uptime
      tags:
      - nodes
  /zos/version:
    get:
      description: Gets the ZOS version
      produces:
      - application/json
      responses:
        "200":
          description: zos version
          schema:
            type: string
        "404":
          description: Not Found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Get ZOS Version
      tags:
      - ZOS
    put:
      consumes:
      - application/json
      description: Sets the ZOS version
      parameters:
      - description: 'Authentication format: Base64(<unix_timestamp>:<twin_id>):Base64(signature)'
        in: header
        name: X-Auth
        required: true
        type: string
      - description: Update ZOS Version Request
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/server.ZOSVersionRequest'
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            additionalProperties: true
            type: object
        "400":
          description: Bad Request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "409":
          description: Conflict
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal Server Error
          schema:
            additionalProperties: true
            type: object
      summary: Set ZOS Version
      tags:
      - ZOS
swagger: "2.0"
