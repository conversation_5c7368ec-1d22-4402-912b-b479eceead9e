// Package docs Code generated by swaggo/swag. DO NOT EDIT
package docs

import "github.com/swaggo/swag"

const docTemplate = `{
    "schemes": {{ marshal .Schemes }},
    "swagger": "2.0",
    "info": {
        "description": "{{escape .Description}}",
        "title": "{{.Title}}",
        "contact": {},
        "version": "{{.Version}}"
    },
    "host": "{{.Host}}",
    "basePath": "{{.BasePath}}",
    "paths": {
        "/accounts": {
            "get": {
                "description": "This endpoint retrieves an account by its twin ID or public key.",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "accounts"
                ],
                "summary": "Retrieve an account by twin ID or public key",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Twin ID of the account",
                        "name": "twin_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Base64 decoded Public key of the account",
                        "name": "public_key",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Account details",
                        "schema": {
                            "$ref": "#/definitions/db.Account"
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Account not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "post": {
                "description": "Create a new twin account with cryptographic verification",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "accounts"
                ],
                "summary": "Create new account",
                "parameters": [
                    {
                        "description": "Account creation data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/server.AccountCreationRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Created account details",
                        "schema": {
                            "$ref": "#/definitions/db.Account"
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "409": {
                        "description": "Account already exists",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/accounts/{twin_id}": {
            "patch": {
                "description": "Updates an account's relays and RMB encryption key",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "accounts"
                ],
                "summary": "Update account details",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Authentication format: Base64(\u003cunix_timestamp\u003e:\u003ctwin_id\u003e):Base64(signature)",
                        "name": "X-Auth",
                        "in": "header",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "Twin ID of the account",
                        "name": "twin_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Account details to update",
                        "name": "account",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/server.UpdateAccountRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Account updated successfully",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Account not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/farms": {
            "get": {
                "description": "Get a list of farms with optional filters",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "farms"
                ],
                "summary": "List farms",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Filter by farm name",
                        "name": "farm_name",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Filter by farm ID",
                        "name": "farm_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Filter by twin ID",
                        "name": "twin_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "Page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Results per page",
                        "name": "size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of farms",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/db.Farm"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "post": {
                "description": "Create a new farm entry",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "farms"
                ],
                "summary": "Create new farm",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Authentication format: Base64(\u003cunix_timestamp\u003e:\u003ctwin_id\u003e):Base64(signature)",
                        "name": "X-Auth",
                        "in": "header",
                        "required": true
                    },
                    {
                        "description": "Farm creation data",
                        "name": "farm",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/db.Farm"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "'farm_id': farmID\"]",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "integer"
                            }
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "409": {
                        "description": "Farm already exists",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/farms/{farm_id}": {
            "get": {
                "description": "Get details for a specific farm",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "farms"
                ],
                "summary": "Get farm details",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Farm ID",
                        "name": "farm_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Farm details",
                        "schema": {
                            "$ref": "#/definitions/db.Farm"
                        }
                    },
                    "400": {
                        "description": "Invalid farm ID",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Farm not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "patch": {
                "description": "Update existing farm details",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "farms"
                ],
                "summary": "Update farm",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Authentication format: Base64(\u003cunix_timestamp\u003e:\u003ctwin_id\u003e):Base64(signature)",
                        "name": "X-Auth",
                        "in": "header",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "Farm ID",
                        "name": "farm_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Farm update data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/server.UpdateFarmRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Farm updated successfully",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Farm not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/nodes": {
            "get": {
                "description": "Get a list of nodes with optional filters",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "nodes"
                ],
                "summary": "List nodes",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Filter by node ID",
                        "name": "node_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Filter by farm ID",
                        "name": "farm_id",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Filter by twin ID",
                        "name": "twin_id",
                        "in": "query"
                    },
                    {
                        "type": "string",
                        "description": "Filter by status",
                        "name": "status",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "Filter by health status",
                        "name": "healthy",
                        "in": "query"
                    },
                    {
                        "type": "boolean",
                        "description": "Filter by online status (true = online, false = offline)",
                        "name": "online",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "description": "Filter nodes last seen within this many minutes",
                        "name": "last_seen",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 1,
                        "description": "Page number",
                        "name": "page",
                        "in": "query"
                    },
                    {
                        "type": "integer",
                        "default": 10,
                        "description": "Results per page",
                        "name": "size",
                        "in": "query"
                    }
                ],
                "responses": {
                    "200": {
                        "description": "List of nodes with online status",
                        "schema": {
                            "type": "array",
                            "items": {
                                "$ref": "#/definitions/db.Node"
                            }
                        }
                    },
                    "400": {
                        "description": "Bad request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "post": {
                "description": "Register a new node in the system",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "nodes"
                ],
                "summary": "Register new node",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Authentication format: Base64(\u003cunix_timestamp\u003e:\u003ctwin_id\u003e):Base64(signature)",
                        "name": "X-Auth",
                        "in": "header",
                        "required": true
                    },
                    {
                        "description": "Node registration data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/server.NodeRegistrationRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "'node_id': nodeID",
                        "schema": {
                            "type": "object",
                            "additionalProperties": {
                                "type": "integer"
                            }
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "409": {
                        "description": "Node already exists",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/nodes/{node_id}": {
            "get": {
                "description": "Get details for a specific node",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "nodes"
                ],
                "summary": "Get node details",
                "parameters": [
                    {
                        "type": "integer",
                        "description": "Node ID",
                        "name": "node_id",
                        "in": "path",
                        "required": true
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Node details with online status and last_seen information",
                        "schema": {
                            "$ref": "#/definitions/db.Node"
                        }
                    },
                    "400": {
                        "description": "Invalid node ID",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Node not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "patch": {
                "description": "Update existing node details",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "nodes"
                ],
                "summary": "Update node",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Authentication format: Base64(\u003cunix_timestamp\u003e:\u003ctwin_id\u003e):Base64(signature)",
                        "name": "X-Auth",
                        "in": "header",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "Node ID",
                        "name": "node_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Node update data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/server.UpdateNodeRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "Node updated successfully",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Node not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/nodes/{node_id}/uptime": {
            "post": {
                "description": "Submit uptime report for a node",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "nodes"
                ],
                "summary": "Report node uptime",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Authentication format: Base64(\u003cunix_timestamp\u003e:\u003ctwin_id\u003e):Base64(signature)",
                        "name": "X-Auth",
                        "in": "header",
                        "required": true
                    },
                    {
                        "type": "integer",
                        "description": "Node ID",
                        "name": "node_id",
                        "in": "path",
                        "required": true
                    },
                    {
                        "description": "Uptime report data",
                        "name": "request",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/server.UptimeReportRequest"
                        }
                    }
                ],
                "responses": {
                    "201": {
                        "description": "Uptime reported successfully",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Invalid request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "404": {
                        "description": "Node not found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        },
        "/zos/version": {
            "get": {
                "description": "Gets the ZOS version",
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ZOS"
                ],
                "summary": "Get ZOS Version",
                "responses": {
                    "200": {
                        "description": "zos version",
                        "schema": {
                            "type": "string"
                        }
                    },
                    "404": {
                        "description": "Not Found",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            },
            "put": {
                "description": "Sets the ZOS version",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "application/json"
                ],
                "tags": [
                    "ZOS"
                ],
                "summary": "Set ZOS Version",
                "parameters": [
                    {
                        "type": "string",
                        "description": "Authentication format: Base64(\u003cunix_timestamp\u003e:\u003ctwin_id\u003e):Base64(signature)",
                        "name": "X-Auth",
                        "in": "header",
                        "required": true
                    },
                    {
                        "description": "Update ZOS Version Request",
                        "name": "body",
                        "in": "body",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/server.ZOSVersionRequest"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "OK",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "400": {
                        "description": "Bad Request",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "401": {
                        "description": "Unauthorized",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "409": {
                        "description": "Conflict",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    },
                    "500": {
                        "description": "Internal Server Error",
                        "schema": {
                            "type": "object",
                            "additionalProperties": true
                        }
                    }
                }
            }
        }
    },
    "definitions": {
        "db.Account": {
            "type": "object",
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "public_key": {
                    "description": "The public key (ED25519 for nodes, ED25519 or SR25519 for farmers) in the more standard base64 since we are moving from substrate echo system?\n(still SS58 can be used or plain base58 ,TBD)",
                    "type": "string"
                },
                "relays": {
                    "description": "Optional list of relay domains",
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "rmb_enc_key": {
                    "description": "Optional base64 encoded public key for rmb communication",
                    "type": "string"
                },
                "twin_id": {
                    "type": "integer"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "db.Farm": {
            "type": "object",
            "required": [
                "farm_name",
                "stellar_address",
                "twin_id"
            ],
            "properties": {
                "created_at": {
                    "type": "string"
                },
                "dedicated": {
                    "type": "boolean"
                },
                "farm_id": {
                    "type": "integer"
                },
                "farm_name": {
                    "type": "string"
                },
                "stellar_address": {
                    "type": "string"
                },
                "twin_id": {
                    "description": "Farmer account reference",
                    "type": "integer"
                },
                "updated_at": {
                    "type": "string"
                }
            }
        },
        "db.Interface": {
            "type": "object",
            "properties": {
                "ips": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "mac": {
                    "type": "string"
                },
                "name": {
                    "type": "string"
                }
            }
        },
        "db.Location": {
            "type": "object",
            "properties": {
                "city": {
                    "type": "string"
                },
                "country": {
                    "type": "string"
                },
                "latitude": {
                    "type": "string"
                },
                "longitude": {
                    "type": "string"
                }
            }
        },
        "db.Node": {
            "type": "object",
            "properties": {
                "approved": {
                    "type": "boolean"
                },
                "created_at": {
                    "type": "string"
                },
                "farm_id": {
                    "description": "Constraints set to prevents unintended account deletion if linked Farms/nodes exist.",
                    "type": "integer"
                },
                "interfaces": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/db.Interface"
                    }
                },
                "last_seen": {
                    "description": "Last time the node sent Uptime report",
                    "type": "string"
                },
                "location": {
                    "$ref": "#/definitions/db.Location"
                },
                "node_id": {
                    "type": "integer"
                },
                "online": {
                    "description": "Computed field, not stored in database",
                    "type": "boolean"
                },
                "resources": {
                    "description": "PublicConfig PublicConfig ` + "`" + `json:\"public_config\" gorm:\"type:json\"` + "`" + `",
                    "allOf": [
                        {
                            "$ref": "#/definitions/db.Resources"
                        }
                    ]
                },
                "secure_boot": {
                    "type": "boolean"
                },
                "serial_number": {
                    "type": "string"
                },
                "twin_id": {
                    "description": "Node account reference",
                    "type": "integer"
                },
                "updated_at": {
                    "type": "string"
                },
                "virtualized": {
                    "type": "boolean"
                }
            }
        },
        "db.Resources": {
            "type": "object",
            "properties": {
                "cru": {
                    "type": "integer"
                },
                "hru": {
                    "type": "integer"
                },
                "mru": {
                    "type": "integer"
                },
                "sru": {
                    "type": "integer"
                }
            }
        },
        "server.AccountCreationRequest": {
            "type": "object",
            "required": [
                "public_key",
                "signature",
                "timestamp"
            ],
            "properties": {
                "public_key": {
                    "description": "base64 encoded",
                    "type": "string"
                },
                "relays": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "rmb_enc_key": {
                    "type": "string"
                },
                "signature": {
                    "description": "the registrar expect a signature of a message with format ` + "`" + `timestampStr:publicKeyBase64` + "`" + `\n- signature format: base64(ed25519_or_sr22519_signature)",
                    "type": "string"
                },
                "timestamp": {
                    "type": "integer"
                }
            }
        },
        "server.NodeRegistrationRequest": {
            "type": "object",
            "required": [
                "farm_id",
                "interfaces",
                "location",
                "resources",
                "serial_number",
                "twin_id"
            ],
            "properties": {
                "farm_id": {
                    "type": "integer",
                    "minimum": 1
                },
                "interfaces": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/db.Interface"
                    }
                },
                "location": {
                    "$ref": "#/definitions/db.Location"
                },
                "resources": {
                    "$ref": "#/definitions/db.Resources"
                },
                "secure_boot": {
                    "type": "boolean"
                },
                "serial_number": {
                    "type": "string"
                },
                "twin_id": {
                    "type": "integer",
                    "minimum": 1
                },
                "virtualized": {
                    "type": "boolean"
                }
            }
        },
        "server.UpdateAccountRequest": {
            "type": "object",
            "properties": {
                "relays": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    }
                },
                "rmb_enc_key": {
                    "type": "string"
                }
            }
        },
        "server.UpdateFarmRequest": {
            "type": "object",
            "properties": {
                "farm_name": {
                    "type": "string",
                    "maxLength": 40
                },
                "stellar_address": {
                    "type": "string"
                }
            }
        },
        "server.UpdateNodeRequest": {
            "type": "object",
            "required": [
                "farm_id",
                "interfaces",
                "location",
                "resources",
                "serial_number"
            ],
            "properties": {
                "farm_id": {
                    "type": "integer",
                    "minimum": 1
                },
                "interfaces": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/db.Interface"
                    }
                },
                "location": {
                    "$ref": "#/definitions/db.Location"
                },
                "resources": {
                    "$ref": "#/definitions/db.Resources"
                },
                "secure_boot": {
                    "type": "boolean"
                },
                "serial_number": {
                    "type": "string"
                },
                "virtualized": {
                    "type": "boolean"
                }
            }
        },
        "server.UptimeReportRequest": {
            "type": "object",
            "required": [
                "timestamp",
                "uptime"
            ],
            "properties": {
                "timestamp": {
                    "type": "integer"
                },
                "uptime": {
                    "type": "integer"
                }
            }
        },
        "server.ZOSVersionRequest": {
            "type": "object",
            "required": [
                "version"
            ],
            "properties": {
                "version": {
                    "type": "string"
                }
            }
        }
    }
}`

// SwaggerInfo holds exported Swagger Info so clients can modify it
var SwaggerInfo = &swag.Spec{
	Version:          "1.0",
	Host:             "",
	BasePath:         "/api/v1",
	Schemes:          []string{},
	Title:            "Node Registrar API",
	Description:      "API for managing TFGrid node registration",
	InfoInstanceName: "swagger",
	SwaggerTemplate:  docTemplate,
	LeftDelim:        "{{",
	RightDelim:       "}}",
}

func init() {
	swag.Register(SwaggerInfo.InstanceName(), SwaggerInfo)
}
