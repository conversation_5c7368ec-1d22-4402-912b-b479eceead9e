name: <PERSON>t sdk

on:
  push:
  workflow_dispatch:

jobs:
  test:
    name: <PERSON> Lint
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: 1.23

      - name: Run golangci-lint
        uses: golangci/golangci-lint-action@v8
        with:
          working-directory: ${{ matrix.dir }}
          args: --config=../.golangci.yml

    strategy:
      fail-fast: false
      matrix:
        dir:
          - node-registrar
