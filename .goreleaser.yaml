# This is an example .goreleaser.yml file with some sensible defaults.
# Make sure to check the documentation at https://goreleaser.com
version: 2
builds:
  - dir: ./node-registrar/cmds
    env:
      - CGO_ENABLED=0
    goos:
      - linux
      - windows
      - darwin
    binary: tfregistrar
    id: tfregistrar

    ignore:
      - goos: windows
    ldflags:
      - -X github.com/threefoldtech/tfgrid4-sdk-go/node-registrar/cmd.version={{.Tag}}
      - -X github.com/threefoldtech/tfgrid-sdk-go/node-registrar/cmd.commit={{.Commit}}

  - dir: ./node-registrar/tools/account
    env:
      - CGO_ENABLED=0
    goos:
      - linux
      - windows
      - darwin
    binary: new-account
    id: new-account

    ignore:
      - goos: windows

  - dir: ./node-registrar/tools/farm
    env:
      - CGO_ENABLED=0
    goos:
      - linux
      - windows
      - darwin
    binary: new-farm
    id: new-farm

    ignore:
      - goos: windows


archives:
  - format: tar.gz
    # this name template makes the OS and Arch compatible with the results of uname.
    name_template: >-
      {{ .ProjectName }}_
      {{- title .Os }}_
      {{- if eq .Arch "amd64" }}x86_64
      {{- else if eq .Arch "386" }}i386
      {{- else }}{{ .Arch }}{{ end }}
      {{- if .Arm }}v{{ .Arm }}{{ end }}
    # use zip for windows archives
    format_overrides:
      - goos: windows
        format: zip
checksum:
  name_template: "checksums.txt"
snapshot:
  version_template: "{{ incpatch .Version }}-next"
changelog:
  sort: asc
  filters:
    exclude:
      - "^docs:"
      - "^test:"
# The lines beneath this are called `modelines`. See `:help modeline`
# Feel free to remove those if you don't want/use them.
# yaml-language-server: $schema=https://goreleaser.com/static/schema.json
# vim: set ts=2 sw=2 tw=0 fo=cnqoj
